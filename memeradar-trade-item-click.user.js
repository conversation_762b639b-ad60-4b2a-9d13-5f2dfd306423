// ==UserScript==
// @name         MemeRadar Trade Item 一键跳转
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  点击trade-item跳转到gmgn.ai查看token详情
// <AUTHOR>
// @match        *://*/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForElement(selector, callback) {
        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                callback(element);
            }
        });
        
        observer.observe(document, {
            childList: true,
            subtree: true
        });
        
        // 如果元素已经存在，直接执行回调
        const existingElement = document.querySelector(selector);
        if (existingElement) {
            observer.disconnect();
            callback(existingElement);
        }
    }

    // 提取token名称的函数
    function extractTokenName(tradeItem) {
        const tokenElement = tradeItem.querySelector('.token');
        if (tokenElement) {
            return tokenElement.textContent.trim();
        }
        return null;
    }

    // 为trade-item添加点击事件
    function addClickHandlers() {
        const tradeItems = document.querySelectorAll('.trade-item');
        
        tradeItems.forEach(item => {
            // 检查是否已经添加过点击事件
            if (item.dataset.clickHandlerAdded) {
                return;
            }
            
            // 添加鼠标悬停样式
            item.style.cursor = 'pointer';
            item.style.transition = 'background-color 0.2s ease';
            
            // 鼠标悬停效果
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });
            
            // 点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是复制图标，不执行跳转
                if (e.target.classList.contains('copy-icon')) {
                    return;
                }
                
                const tokenName = extractTokenName(this);
                if (tokenName) {
                    const url = `https://gmgn.ai/sol/token/${tokenName}`;
                    window.open(url, '_blank');
                } else {
                    console.warn('无法提取token名称');
                }
            });
            
            // 标记已添加点击事件
            item.dataset.clickHandlerAdded = 'true';
        });
    }

    // 监听DOM变化，处理动态加载的内容
    function observeTradeItems() {
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查是否添加了新的trade-item
                            if (node.classList && node.classList.contains('trade-item')) {
                                shouldUpdate = true;
                            } else if (node.querySelector && node.querySelector('.trade-item')) {
                                shouldUpdate = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldUpdate) {
                setTimeout(addClickHandlers, 100); // 延迟一点执行，确保DOM完全更新
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化
    function init() {
        // 等待smart-panel出现
        waitForElement('.smart-panel', () => {
            console.log('MemeRadar Trade Item 点击脚本已加载');
            
            // 初始添加点击事件
            addClickHandlers();
            
            // 开始监听DOM变化
            observeTradeItems();
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
