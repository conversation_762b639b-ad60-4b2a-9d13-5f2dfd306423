// ==UserScript==
// @name         MemeRadar Trade Item 一键跳转
// @namespace    http://tampermonkey.net/
// @version      1.0
// @description  点击trade-item跳转到gmgn.ai查看token详情
// <AUTHOR>
// @match        *://*/*
// @grant        GM_setClipboard
// @grant        GM_getClipboard
// @grant        unsafeWindow
// ==/UserScript==

(function() {
    'use strict';

    // 等待页面加载完成
    function waitForElement(selector, callback) {
        const observer = new MutationObserver((mutations, obs) => {
            const element = document.querySelector(selector);
            if (element) {
                obs.disconnect();
                callback(element);
            }
        });
        
        observer.observe(document, {
            childList: true,
            subtree: true
        });
        
        // 如果元素已经存在，直接执行回调
        const existingElement = document.querySelector(selector);
        if (existingElement) {
            observer.disconnect();
            callback(existingElement);
        }
    }

    // 提取token名称的函数
    function extractTokenName(tradeItem) {
        const tokenElement = tradeItem.querySelector('.token');
        if (tokenElement) {
            return tokenElement.textContent.trim();
        }
        return null;
    }

    // 为copy-icon添加点击事件
    function addCopyIconHandlers() {
        const copyIcons = document.querySelectorAll('.copy-icon');

        copyIcons.forEach(icon => {
            // 检查是否已经添加过点击事件
            if (icon.dataset.clickHandlerAdded) {
                return;
            }

            // 添加鼠标悬停样式
            icon.style.cursor = 'pointer';
            icon.style.transition = 'opacity 0.2s ease';

            // 鼠标悬停效果
            icon.addEventListener('mouseenter', function() {
                this.style.opacity = '0.7';
            });

            icon.addEventListener('mouseleave', function() {
                this.style.opacity = '1';
            });

            // 点击事件
            icon.addEventListener('click', async function(e) {
                e.preventDefault();
                e.stopPropagation();

                try {
                    // 等待一小段时间让原始复制操作完成
                    setTimeout(async () => {
                        try {
                            // 读取剪贴板内容
                            let clipboardText = '';

                            // 尝试使用现代API（需要用户授权）
                            if (navigator.clipboard && navigator.clipboard.readText) {
                                try {
                                    clipboardText = await navigator.clipboard.readText();
                                } catch (permissionError) {
                                    console.log('剪贴板权限被拒绝，尝试备用方案');
                                    // 如果权限被拒绝，尝试备用方案
                                    if (typeof GM_getClipboard !== 'undefined') {
                                        clipboardText = GM_getClipboard();
                                    }
                                }
                            }
                            // 备用方案：使用Tampermonkey的API
                            else if (typeof GM_getClipboard !== 'undefined') {
                                clipboardText = GM_getClipboard();
                            }

                            // 如果以上方法都失败，提示用户手动操作
                            if (!clipboardText) {
                                const userInput = prompt('无法自动读取剪贴板，请手动粘贴代币地址:');
                                if (userInput) {
                                    clipboardText = userInput.trim();
                                }
                            }

                            console.log('剪贴板内容:', clipboardText);

                            if (clipboardText && clipboardText.trim()) {
                                // 检查是否是有效的Solana地址格式（通常是44个字符的base58编码）
                                const address = clipboardText.trim();
                                if (address.length >= 32 && address.length <= 44) {
                                    const url = `https://gmgn.ai/sol/token/${address}`;
                                    window.open(url, '_blank');
                                    console.log('跳转到:', url);
                                } else {
                                    console.warn('剪贴板内容不像是有效的代币地址:', address);
                                }
                            } else {
                                console.warn('剪贴板为空或无法读取');
                            }
                        } catch (error) {
                            console.error('读取剪贴板失败:', error);
                        }
                    }, 100);

                } catch (error) {
                    console.error('处理复制图标点击失败:', error);
                }
            });

            // 标记已添加点击事件
            icon.dataset.clickHandlerAdded = 'true';
        });
    }

    // 为trade-item添加点击事件（保留原有功能）
    function addClickHandlers() {
        const tradeItems = document.querySelectorAll('.trade-item');

        tradeItems.forEach(item => {
            // 检查是否已经添加过点击事件
            if (item.dataset.clickHandlerAdded) {
                return;
            }

            // 添加鼠标悬停样式
            item.style.cursor = 'pointer';
            item.style.transition = 'background-color 0.2s ease';

            // 鼠标悬停效果
            item.addEventListener('mouseenter', function() {
                this.style.backgroundColor = 'rgba(0, 0, 0, 0.05)';
            });

            item.addEventListener('mouseleave', function() {
                this.style.backgroundColor = '';
            });

            // 点击事件
            item.addEventListener('click', function(e) {
                // 如果点击的是复制图标，不执行跳转（因为复制图标有自己的处理逻辑）
                if (e.target.classList.contains('copy-icon')) {
                    return;
                }

                const tokenName = extractTokenName(this);
                if (tokenName) {
                    const url = `https://gmgn.ai/sol/token/${tokenName}`;
                    window.open(url, '_blank');
                } else {
                    console.warn('无法提取token名称');
                }
            });

            // 标记已添加点击事件
            item.dataset.clickHandlerAdded = 'true';
        });
    }

    // 监听DOM变化，处理动态加载的内容
    function observeTradeItems() {
        const observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            // 检查是否添加了新的trade-item
                            if (node.classList && node.classList.contains('trade-item')) {
                                shouldUpdate = true;
                            } else if (node.querySelector && node.querySelector('.trade-item')) {
                                shouldUpdate = true;
                            }
                        }
                    });
                }
            });
            
            if (shouldUpdate) {
                setTimeout(() => {
                    addClickHandlers();
                    addCopyIconHandlers();
                }, 100); // 延迟一点执行，确保DOM完全更新
            }
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    // 初始化
    function init() {
        // 等待smart-panel出现
        waitForElement('.smart-panel', () => {
            console.log('MemeRadar Trade Item 点击脚本已加载');
            
            // 初始添加点击事件
            addClickHandlers();
            addCopyIconHandlers();

            // 开始监听DOM变化
            observeTradeItems();
        });
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
